"""
GestureFlow - Main Entry Point
Webcam-Based Hand Gesture Computer Control Software

This is the main entry point for the GestureFlow application.
GFLOW-1: Setup Project & Integrate Webcam Input
"""

import sys
import logging
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

from src.ui.main_window import MainWindow


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('gestureflow.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )


def main():
    """Main application entry point."""
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)

    logger.info("Starting GestureFlow application...")

    # Enable high DPI scaling before creating QApplication
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    # Create Qt application
    app = QApplication(sys.argv)
    app.setApplicationName("GestureFlow")
    app.setApplicationVersion("0.1.0")

    try:
        # Create and show main window
        main_window = MainWindow()
        main_window.show()

        logger.info("GestureFlow application started successfully")

        # Run the application
        exit_code = app.exec_()

        logger.info(f"GestureFlow application exited with code: {exit_code}")
        return exit_code

    except Exception as e:
        logger.error(f"Error starting application: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
